# 💬 Система текстов раннего вовлечения - Первая итерация

> **Статус:** Первая итерация концепции. Будет дорабатываться.

## 🎯 Цель системы

Создать **мотивирующие и проникновенные тексты**, которые точно отражают ситуацию пользователя и максимально эффективно возвращают его к работе над приоритетным проектом.

## 🏗️ Архитектура системы

### **Базовая матрица 5×4 + Streak-модификаторы**

**Основа:** Матрица 5×4 (20 базовых сообщений)
- **ВЕРТИКАЛЬ (0-4):** Дни без работы над приоритетным проектом
- **ГОРИЗОНТАЛЬ (0-3):** Номер попытки в течение дня (эскалация)

**Дополнение:** Streak-модификаторы для непрерывных дней работы

### **Принцип работы:**
1. **Базовый текст** выбирается из матрицы по координатам (дни без работы × попытка)
2. **Streak-модификатор** добавляется в начало для особых дней
3. **Рандомизация** - случайный выбор из нескольких вариантов заголовков/подзаголовков
4. **Подстановка переменных** - [focused_project], [current_bar], и т.д.

## 📏 Ограничения UI

- **Заголовок:** максимум 50 символов
- **Подзаголовок:** максимум 120 символов  
- **Кнопка:** максимум 25 символов

> ⚠️ **Важно:** Превышение лимитов приведет к обрезанию текста в интерфейсе

## 🔥 Streak-система (непрерывные дни работы)

### **Ключевые дни с уникальными сообщениями:**
- **День 2:** "Хорошее начало!"
- **День 3:** "Третий день подряд!"
- **День 7:** "Отличная неделя!"
- **День 14:** "Две недели стабильности!"
- **День 30:** "Месяц продуктивности - вы в топе!"
- **День 50:** "Полтора месяца постоянства!"
- **День 100:** "Сотня дней - это мастерство!"

### **Промежуточные дни (универсальные):**
- Дни 4-6: "Так держать, уже X дней!"
- Дни 8-13: "Уже X дней подряд!"
- Дни 15-29: "X дней стабильной работы!"
- И т.д.

### **Логика streak:**
- **Только непрерывные дни** работы над приоритетным проектом
- **Сброс при пропуске:** Если день без работы → streak = 0
- **Минимум для streak:** Хотя бы один завершенный интервал в день

## 📋 Матрица текстов по уровням

### **🟢 Уровень 0: Сегодня уже работал**
*Поддерживающие сообщения*

### **🟡 Уровень 1: 1 день без работы - КРИТИЧЕСКИЙ**
> **Философия:** Развилка - либо возвращение, либо деградация на недели

**Примеры заголовков:**
- "Один день - не страшно!"
- "Возвращаемся в ритм"
- "Новый день - новый старт"

**Примеры подзаголовков:**
- "Сейчас лучшее время вернуться. Мозг свежий, сопротивления минимум"
- "Важно начать сегодня, пока не превратилось в привычку откладывать"

**Эскалация по горизонтали:**
1. **1-е сообщение:** Мотивация + "лучшее время сейчас"
2. **2-е сообщение:** "Шанс еще есть" + "сниженная планка"
3. **3-е сообщение:** "Совсем короткий интервал" + "сохранит momentum"
4. **4-е сообщение:** "Хотя бы минимум" + "просто открыть проект"

### **🟠 Уровень 2-3: 2-3 дня без работы**
**Философия:** "Пора возвращаться! Несколько дней перерыва - не критично, но затягивать нельзя"

### **🔴 Уровень 4-6: 4-6 дней без работы**
**Философия:** "Серьезно, пора работать! Почти неделя без прогресса - время что-то менять"

### **🚨 Уровень 7+: Неделя+ без работы**
**Философия:** "КРИТИЧЕСКАЯ СИТУАЦИЯ! Неделя без работы. Этот проект все еще важен?"

## 🔧 Решение проблемы одинаковых планок

**Проблема:** 3-е и 4-е сообщения могут иметь одинаковую планку (3 мин)

**Решение:** Фокус на **контексте**, а не размере планки:
- **3-е сообщение:** "сниженный интервал" / "попробуем меньше"
- **4-е сообщение:** "минимальный вариант" / "хотя бы что-то" / "не прервать streak"

## 🎲 Система рандомизации

### **Множественные варианты для каждой ячейки:**
```json
{
  "1": { // 1 день без работы
    "0": { // 1-е сообщение
      "titles": [
        "Один день - не страшно!",
        "Возвращаемся в ритм", 
        "Новый день - новый старт"
      ],
      "subtitles": [
        "Сейчас лучшее время вернуться...",
        "Важно начать сегодня...",
        "Утренняя энергия + сниженная планка..."
      ]
    }
  }
}
```

### **Алгоритм выбора:**
1. Определить координаты в матрице (дни без работы × попытка)
2. Проверить streak и добавить модификатор если нужно
3. Случайно выбрать заголовок и подзаголовок
4. Подставить переменные [focused_project], [current_bar]

## 🔄 Интеграция с существующей системой

### **Изменения в коде:**
- **MessageConstructionMatrix.swift** → новая архитектура с JSON
- **EarlyEngagementSystem.swift** → добавить streak-логику
- **Отладочное окно** → добавить выбор streak для тестирования

### **Новые компоненты:**
- **StreakManager** → отслеживание непрерывных дней работы
- **TextVariationManager** → рандомизация вариантов текстов
- **MessagePersonalizationManager** → подстановка переменных + streak-модификаторы

## 🔧 Техническая архитектура streak-системы

### **Проблема типов данных:**
- **Дни без работы** = дискретные уровни (0, 1, 2-3, 4-6, 7+)
- **Streak дней** = точное число (1, 2, 3, 4, 5... 1000)

### **Решение для отладочного окна:**
```
┌─ Режим тестирования ─────────────────┐
│ ○ Дни без работы (выпадающий список) │
│   └─ [1 день] [2-3 дня] [4-6 дней]  │
│                                      │
│ ○ Streak дней (числовое поле)        │
│   └─ [  5  ] ← ↑ ↓ (стрелочки)      │
└──────────────────────────────────────┘
```

**Логика:**
- Выбираешь **либо** "дни без работы" **либо** "streak"
- Если streak > 0 → автоматически "дни без работы = 0"
- Если "дни без работы > 0" → автоматически "streak = 0"

### **Новая архитектура уровней:**
```swift
enum EngagementLevel {
    case workingToday(streakDays: Int)  // НОВЫЙ! Заменяет старый уровень 0
    case oneDayOff                      // Старый уровень 1
    case twoDaysOff                     // Старый уровень 2-3
    case weekOff                        // Старый уровень 4-6
    case criticalOff                    // Старый уровень 7+
}
```

### **Логика выбора сообщений:**
```swift
func getMessage(level: EngagementLevel, attemptIndex: Int) -> EngagementMessage {
    switch level {
    case .workingToday(let streakDays):
        return getStreakMessage(streakDays: streakDays, attemptIndex: attemptIndex)
    case .oneDayOff:
        return getMatrix(vertical: 1, horizontal: attemptIndex)
    // ...
    }
}
```

### **Streak-сообщения:**

**Ключевые дни (специальные сообщения):**
- День 1: "Хорошее начало!"
- День 2: "Второй день подряд!"
- День 3: "Третий день - формируется привычка!"
- День 7: "Неделя стабильной работы!"
- День 14: "Две недели - это уже система!"
- День 30: "Месяц продуктивности!"
- День 50: "Полтора месяца постоянства!"
- День 100: "Сотня дней - мастерский уровень!"

**Промежуточные дни (обычные сообщения):**
```swift
"Так держать! Уже {streakDays} дней продуктивной работы. Продолжаем?"
```

### **⚠️ ВАЖНО: Миграция существующего кода**

**Текущее состояние:** Уровень 0 = "сегодня уже работал" (дискретная категория)
**Новое состояние:** `.workingToday(streakDays: Int)` (точное число)

### **Анализ существующего кода:**

**1. EarlyEngagementSystem.swift:**
- `lastWorkTime: Date?` - время последней работы над приоритетным проектом
- `daysWithoutWork: Int` - количество дней без работы (0 = сегодня работал)
- `calculateDaysWithoutWork()` - вычисляет дни без работы на основе `lastWorkTime`
- `handleIntervalCompletion()` - обновляет `lastWorkTime` при завершении интервала
- `getDaysLevelIndex()` - маппинг дней без работы в индексы матрицы (0-4)

**2. MessageConstructionMatrix.swift:**
- Уровень 0 (строки 12-50): 4 сообщения для "сегодня уже работал"
- Все сообщения имеют `level: 0, timeOfDay: 0-3`

**3. ButtonMatrix.swift:**
- `case 0:` (строки 88-92): логика для уровня 0 с GradualGrowthSystem
- Использует ту же логику что и EarlyEngagementSystem

**4. Система персистентности:**
- `loadLastWorkTime()` / `saveLastWorkTime()` - UserDefaults
- Ключ: `"earlyEngagement_lastWork"`

### **Что нужно добавить для streak:**

**1. Новые методы в EarlyEngagementSystem:**
```swift
private func calculateCurrentStreak() -> Int {
    // Подсчет непрерывных дней работы
    // Анализ истории lastWorkTime за последние дни
}

private func loadStreakData() {
    // Загрузка истории работы для подсчета streak
}

private func saveStreakData() {
    // Сохранение истории для streak
}
```

**2. Новая структура данных:**
```swift
struct WorkDay: Codable {
    let date: Date
    let workedOnPriorityProject: Bool
}

private var workHistory: [WorkDay] = []
```

**3. Модификация логики уровней:**
```swift
// Вместо:
let verticalLevel = getDaysLevelIndex(daysWithoutWork: daysWithoutWork)

// Будет:
let engagementLevel = determineEngagementLevel()
switch engagementLevel {
case .workingToday(let streakDays):
    // Новая логика для streak
case .oneDayOff, .twoDaysOff, etc.:
    // Существующая логика
}
```

**Влияние на компоненты:**
- ✅ **MessageConstructionMatrix** - заменить уровень 0 на streak-логику
- ✅ **ButtonMatrix** - адаптировать case 0 для streak
- ✅ **EarlyEngagementSystem** - добавить streak-вычисления
- ✅ **Отладочное окно** - новый UI для streak
- ✅ **Персистентность** - новые ключи для истории работы

## 🔄 План миграции

### **Этап 1: Подготовка streak-инфраструктуры**
1. Добавить `WorkDay` структуру и `workHistory` массив
2. Реализовать `calculateCurrentStreak()` метод
3. Добавить методы загрузки/сохранения streak-данных
4. Создать `determineEngagementLevel()` для новой логики

### **Этап 2: Создание streak-сообщений**
1. Создать `StreakMessageMatrix` для специальных дней
2. Создать `GenericStreakMessage` для промежуточных дней
3. Реализовать `getStreakMessage(streakDays:, attemptIndex:)`

### **Этап 3: Модификация существующих компонентов**
1. **MessageConstructionMatrix:** убрать уровень 0, добавить streak-логику
2. **ButtonMatrix:** адаптировать case 0 для streak
3. **EarlyEngagementSystem:** заменить `getDaysLevelIndex` на `determineEngagementLevel`

### **Этап 4: Обновление отладочного окна**
1. Добавить радиокнопки для выбора режима
2. Добавить числовое поле для streak
3. Обновить логику тестирования

### **Этап 5: Тестирование и откат**
1. Протестировать все сценарии
2. Подготовить план отката если что-то сломается
3. Постепенное внедрение с возможностью переключения

## ❓ Открытые вопросы

1. **Структура данных:** JSON файл или Swift структуры?
2. **Классификация текстов:** Что считать заголовком, что подзаголовком в примерах?
3. **Время дня:** Пока не учитываем, но как подготовить архитектуру для будущего?
4. **История работы:** Сколько дней хранить для подсчета streak? (30? 100?)
5. **Переходный период:** Как обработать пользователей с существующими данными?

## 🚀 Следующие шаги

1. **Доработать концепцию** на основе обратной связи
2. **Написать конкретные тексты** для всех 20 ячеек матрицы
3. **Создать финальную документацию** с полной спецификацией
4. **Реализовать техническую архитектуру**
5. **Интегрировать с отладочным окном** для тестирования

---

## 🚀 Статус реализации (обновлено 31.07.2025)

### ✅ ПОЛНОСТЬЮ ЗАВЕРШЕНО
- **Анализ существующей системы** - детальный анализ кода и архитектуры
- **Архитектурное планирование** - план миграции и новая структура
- **Документирование текущего состояния** - полная техническая документация

### ✅ STREAK-ИНФРАСТРУКТУРА РЕАЛИЗОВАНА
- **Структуры данных:**
  - `WorkDay` struct для отслеживания истории работы
  - `EngagementLevel` enum для мостика между старой и новой системами
  - Новые свойства: `workHistory`, `currentStreak`, `maxWorkHistoryDays`

- **Методы расчета streak:**
  - `calculateCurrentStreak()` - подсчет непрерывных дней работы
  - `updateWorkHistoryForToday()` - обновление истории при завершении интервала
  - `loadWorkHistory()`, `saveWorkHistory()`, `cleanupOldWorkHistory()` - персистентность

- **Отладочные методы:**
  - `debugSetStreakDays()`, `debugClearStreakDays()`, `debugGetCurrentStreak()`
  - `debugGetWorkHistory()`, `debugGetEngagementLevel()`, `debugResetWorkHistory()`

- **Интеграция с системой сообщений:**
  - Обновлен `createEngagementMessage()` для поддержки streak-режима
  - Добавлена подстановка переменной `[streak_days]` в `substituteVariables()`
  - Логика определения режима (streak vs дни без работы)

### ⚠️ ВАЖНО: Текущие ограничения
**Специальные сообщения для ключевых дней (2, 3, 7, 14, 30, 50, 100) пока НЕ реализованы.**

**Текущее поведение streak-системы:**
- ✅ Корректно подсчитывает и хранит streak
- ✅ Подставляет `[streak_days]` в тексты
- ✅ Работает через отладочное окно
- ❌ Использует обычные сообщения уровня 0 (не специальные для ключевых дней)

**Пример:** При streak = 7 дней система покажет обычное сообщение уровня 0 с подстановкой "7", но НЕ специальное "Отличная неделя!"

### ✅ ОТЛАДОЧНОЕ ОКНО ОБНОВЛЕНО
- **Новый UI:**
  - Радиокнопки для выбора режима: "Дни без работы" / "Streak дней"
  - Поле ввода и stepper для установки streak значений
  - Автоматическое переключение между режимами

- **Обновленная логика:**
  - `updateMatrixMessage()` - поддержка обоих режимов
  - `showMatrixMessage()` - корректная установка отладочных параметров
  - Очистка streak во всех колбэках окна сообщений

### ✅ ТЕСТИРОВАНИЕ
- **Создан тест:** `Tests/StreakInfrastructureTest.swift`
- **Проверяет:** отладочные методы, создание сообщений, подстановку переменных
- **Статус:** Готов к запуску через отладочное окно

### 🔄 В ПРОЦЕССЕ
- **Ручное тестирование** streak-инфраструктуры через отладочное окно
- **Проверка интеграции** всех компонентов в реальном приложении

### 📋 СЛЕДУЮЩИЕ ЭТАПЫ
1. **ПРИОРИТЕТ: Реализация специальных сообщений для ключевых дней streak**
   - Логика выбора между специальными и обычными сообщениями
   - Фактические тексты для дней 2, 3, 7, 14, 30, 50, 100
   - Интеграция с существующей системой сообщений

2. **Создание гибкой системы текстовой матрицы** - замена hardcoded сообщений
3. **Поддержка множественных вариантов текстов** - рандомизация
4. **Интеграция с системой случайного выбора** - финальная архитектура

---

**Примечание:** Streak-инфраструктура полностью готова. Следующий этап - создание новой системы текстов.
