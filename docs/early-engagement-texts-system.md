# 💬 Система текстов раннего вовлечения

> **Статус:** ✅ Streak-система реализована. Элегантное решение матрицы заголовков/подзаголовков готово к реализации.

> **🎭 ЭЛЕГАНТНОЕ РЕШЕНИЕ:** Архитектура матрицы заголовков и подзаголовков описана в → **[message-matrix-elegant-solution.md](./message-matrix-elegant-solution.md)**

## 🎯 Цель системы

Создать **мотивирующие и проникновенные тексты**, которые точно отражают ситуацию пользователя и максимально эффективно возвращают его к работе над приоритетным проектом.

## 🎭 Ключевая идея: Элегантное разделение ответственности

**ЗАГОЛОВОК** = Уровень (философия) + Горизонталь (срочность) = уникальный для каждой ситуации
**ПОДЗАГОЛОВОК** = Универсальная горизонтальная эскалация = 4 варианта на ВСЕ уровни

**Результат:** 24 уникальных текста вместо 40+ в полной матрице = **экономия 40%**

## 🏗️ Архитектура системы

### **🔄 Универсальные подзаголовки (горизонтальная эскалация):**
> **Работают для ВСЕХ уровней и режимов**

1. **1-е сообщение:** "Лучшее время начать - прямо сейчас"
2. **2-е сообщение:** "Шанс еще есть, но время идет"
3. **3-е сообщение:** "Время уходит, но еще можно"
4. **4-е сообщение:** "Последний шанс начать сегодня"

### **Два режима работы:**

**🔥 STREAK-РЕЖИМ** (работал сегодня):
- **Заголовки:** 4 базовых с переменными + специальные для ключевых дней
- **Подзаголовки:** Те же 4 универсальных
- **Переменные:** `[streak_days]` с правильным склонением (день/дня/дней)
- **Специальные дни:** 7, 14, 30, 50, 100 (переопределяют базовые заголовки)

**⚠️ ВОЗВРАЩЕНИЕ** (дни без работы):
- **Заголовки:** 16 уникальных (4 уровня × 4 горизонтали)
- **Подзаголовки:** Те же 4 универсальных
- **Уровни:** 1 день, 2-3 дня, 4-6 дней, 7+ дней
- **Философия:** Каждый уровень имеет свой тон и подход

## 🔥 STREAK-РЕЖИМ: Непрерывные дни работы

### **📈 Базовые заголовки с переменными:**
1. **1-е сообщение:** "Уже [streak_days] [день/дня/дней] работы!"
2. **2-е сообщение:** "Не хочется терять [streak_days] [день/дня/дней] прогресса"
3. **3-е сообщение:** "[streak_days] [день/дня/дней] подряд - отличный темп!"
4. **4-е сообщение:** "Streak [streak_days] [день/дня/дней] - продолжаем!"

### **🎉 Специальные заголовки для ключевых дней:**
> **Переопределяют базовые для особых дат**

- **День 7:** "Целая неделя работы!" (вместо "Уже 7 дней работы!")
- **День 14:** "Две недели стабильности!" (вместо "Уже 14 дней работы!")
- **День 30:** "Месяц продуктивности!" (вместо "Уже 30 дней работы!")
- **День 50:** "Полтора месяца постоянства!" (вместо "Уже 50 дней работы!")
- **День 100:** "Сотня дней мастерства!" (вместо "Уже 100 дней работы!")

### **📝 Правила склонения:**
- **1, 21, 31, 41...** → "день" (1 день, 21 день)
- **2-4, 22-24, 32-34...** → "дня" (2 дня, 3 дня, 23 дня)
- **5-20, 25-30, 35-40...** → "дней" (5 дней, 10 дней, 25 дней)

### **✅ Что реализовано:**
- Подсчет streak (непрерывных дней работы)
- Подстановка `[streak_days]` в тексты уровня 0
- Интеграция с ButtonMatrix для расчета планок
- Правила streak: только непрерывные дни, сброс при пропуске, минимум 1 интервал в день

## ⚠️ РЕЖИМ ВОЗВРАЩЕНИЯ: Дни без работы

### **� Уровень 1: 1 день без работы**
**Философия:** "Развилка - либо возвращение, либо деградация"

**Заголовки (4 варианта по горизонтали):**
1. **1-е сообщение:** "Вчера не работал - не страшно!"
2. **2-е сообщение:** "Давай начинать! Вчера пропустил, но шанс есть"
3. **3-е сообщение:** "Не хочешь выпадать из ритма?"
4. **4-е сообщение:** "Один день может стать неделей..."

### **� Уровень 2: 2-3 дня без работы**
**Философия:** "Пора возвращаться! Перерыв не критичен, но затягивать нельзя"

**Заголовки (4 варианта по горизонтали):**
1. **1-е сообщение:** "Пора возвращаться к работе"
2. **2-е сообщение:** "Перерыв затягивается..."
3. **3-е сообщение:** "Уже [X] дня без прогресса"
4. **4-е сообщение:** "Не дай перерыву стать привычкой"

### **� Уровень 3: 4-6 дней без работы**
**Философия:** "Серьезно, пора работать! Почти неделя без прогресса"

**Заголовки (4 варианта по горизонтали):**
1. **1-е сообщение:** "Серьезно, пора работать!"
2. **2-е сообщение:** "Почти неделя без прогресса..."
3. **3-е сообщение:** "Ситуация выходит из-под контроля"
4. **4-е сообщение:** "Неделя на носу - последний шанс"

### **� Уровень 4: 7+ дней без работы**
**Философия:** "КРИТИЧЕСКАЯ СИТУАЦИЯ! Этот проект все еще важен?"

**Заголовки (4 варианта по горизонтали):**
1. **1-е сообщение:** "Неделя без работы - но еще не поздно"
2. **2-е сообщение:** "Этот проект все еще важен?"
3. **3-е сообщение:** "Критическая ситуация с проектом"
4. **4-е сообщение:** "Неделя без работы... Последний шанс?"

> **Особенность уровня 4:** Все горизонтальные позиции = 3 минуты (план минимум)


## 🎭 Принцип работы элегантного решения

### **Разделение ответственности:**
- **ЗАГОЛОВОК** = Философия уровня + Горизонтальная срочность (уникальный для каждой ситуации)
- **ПОДЗАГОЛОВОК** = Универсальная временная эскалация (4 варианта на все ситуации)

> ⚠️ **КРИТИЧЕСКИ ВАЖНО:** При создании всех текстов учитывать [проблему сопротивления пользователя](problems/user-resistance-and-sabotage.md). Избегать командного тона, подчеркивать автономию пользователя. Но самое главное - иногда напоминать что система это помощник. 

### **🔄 Универсальные подзаголовки (СЛОЙ 1):**
> **НЕ упоминаем конкретное время сессии в тексте!**

1. **1-е сообщение:** "Лучшее время начать - прямо сейчас" (100% планки)
2. **2-е сообщение:** "Шанс еще есть, но время идет" (50% планки)
3. **3-е сообщение:** "Время уходит, но еще можно" (25% планки)
4. **4-е сообщение:** "Последний шанс начать сегодня" (3 минуты - план минимум)

### **📊 Философия уровней (СЛОЙ 2):**

- **Streak:** Поддержка и мотивация продолжать
- **Уровень 1 (1 день):** Развилка - либо возвращение, либо деградация
- **Уровень 2 (2-3 дня):** Пора возвращаться! Затягивать нельзя
- **Уровень 3 (4-6 дней):** Серьезно, пора работать! Почти неделя без прогресса
- **Уровень 4 (7+ дней):** КРИТИЧЕСКАЯ СИТУАЦИЯ! Проект все еще важен?

## 🎨 Примеры работы системы

### **Пример 1: Возвращение, уровень 1, 1-е сообщение**
- **Заголовок:** "Вчера не работал - не страшно!"
- **Подзаголовок:** "Лучшее время начать - прямо сейчас"

### **Пример 2: Возвращение, уровень 4, 4-е сообщение**
- **Заголовок:** "Неделя без работы... Последний шанс?"
- **Подзаголовок:** "Последний шанс начать сегодня"

### **Пример 3: Streak 5 дней, 2-е сообщение**
- **Заголовок:** "Не хочется терять 5 дней прогресса" (5 → "дней")
- **Подзаголовок:** "Шанс еще есть, но время идет"

### **Пример 4: Streak 30 дней, 1-е сообщение**
- **Заголовок:** "Месяц продуктивности!" (специальный)
- **Подзаголовок:** "Лучшее время начать - прямо сейчас"



## 🔧 Техническая реализация

### **Элегантная архитектура:**
- **Заголовки:** 16 для возвращения + 4 базовых для streak + специальные для ключевых дней
- **Подзаголовки:** 4 универсальных для всех ситуаций
- **Склонение:** Функция `getDaysDeclension()` для правильных падежей

### **Алгоритм выбора сообщения:**
1. **Определить режим:** Streak (работал сегодня) или Возвращение (дни без работы)
2. **Выбрать заголовок:** По уровню и горизонтали (для возвращения) или по дням streak
3. **Выбрать подзаголовок:** Универсальный по горизонтальной позиции
4. **Подставить переменные:** [streak_days], [focused_project] с правильным склонением

## � План реализации элегантного решения

### **Этап 1: Разработка философии уровней**
- [ ] Детализировать философию каждого уровня возвращения
- [ ] Определить тональность и подход для streak-режима
- [ ] Создать guidelines для написания заголовков

### **Этап 2: Создание заголовков**
- [ ] **Streak:** 4 базовых + 5 специальных для ключевых дней
- [ ] **Возвращение:** 16 уникальных заголовков (4 уровня × 4 горизонтали)
- [ ] Каждый заголовок отражает философию уровня + горизонтальную срочность

### **Этап 3: Техническая интеграция**
- [ ] Реализовать функцию склонения `getDaysDeclension()`
- [ ] Настроить систему подстановки переменных
- [ ] Интегрировать универсальные подзаголовки

### **Этап 4: Тестирование**
- [ ] Проверить все 24 комбинации (20 возвращение + 4 streak)
- [ ] Протестировать специальные дни streak
- [ ] Убедиться в правильном склонении

### **Этап 5: Полировка**
- [ ] Финальная проверка всех текстов
- [ ] Настройка отображения заголовок + подзаголовок
- [ ] Документирование финальной версии

## 📏 Ограничения UI

- **Заголовок:** максимум 50 символов
- **Подзаголовок:** максимум 120 символов
- **Кнопка:** максимум 25 символов

> ⚠️ **Важно:** Превышение лимитов приведет к обрезанию текста в интерфейсе

## 📋 Переменные для подстановки

### **Доступные переменные:**
- `[focused_project]` - название приоритетного проекта
- `[streak_days]` - количество дней streak с правильным склонением

### **Примеры использования:**
- "Уже [streak_days] работы над [focused_project]!"
- "Не хочется терять [streak_days] прогресса"

## ⚠️ Текущий статус

### **✅ Что реализовано:**
- Полностью реализована streak-инфраструктура
- Корректно подсчитывает и хранит streak (непрерывные дни работы)
- Подставляет `[streak_days]` в тексты уровня 0
- ButtonMatrix корректно рассчитывает планки для streak (vertical = -1)
- Система персистентности (WorkDay, workHistory)
- **Элегантное решение архитектуры** заголовков/подзаголовков

### **🎯 Готово к реализации:**
- **Архитектура элегантного решения** полностью спроектирована
- **24 текста вместо 40+** = экономия 40% работы
- **Универсальные подзаголовки** для всех ситуаций
- **Правила склонения** для русского языка
- **План реализации** из 5 этапов

### **📋 Следующий шаг:**
Начать **Этап 1** - разработку философии уровней и создание guidelines для написания заголовков согласно элегантному решению.
