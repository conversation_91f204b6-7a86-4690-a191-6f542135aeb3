# 💬 Система текстов раннего вовлечения

> **Статус:** ✅ Streak-система реализована. Специальные тексты для ключевых дней - в разработке.

## 🎯 Цель системы

Создать **мотивирующие и проникновенные тексты**, которые точно отражают ситуацию пользователя и максимально эффективно возвращают его к работе над приоритетным проектом.

## 🏗️ Архитектура системы

### **Два режима работы:**

**🔥 STREAK-РЕЖИМ** (работал сегодня):
- Система сообщений простая. Нет сложных матриц. Просто 2 варианта: 
  - Поддерживающие сообщения для непрерывных дней работы
  - Специальные тексты для ключевых дней (2, 3, 7, 14, 30, 50, 100)
- Вопрос лишь в том, как формировать заголовки и тексты сообщений. 
- Ну и горизонталь все равно присутствует
- Подстановка переменной `[streak_days]` в тексты

**⚠️ ВОЗВРАЩЕНИЕ** (дни без работы):
- Матрица 4×4 для уровней 1-4 (1 день, 2-3 дня, 4-6 дней, 7+ дней)
- Эскалация по горизонтали: 4 попытки в день с уменьшением планки
- Принцип: НЕ привязываемся к времени сессии, говорим "сниженный интервал"

## 🔥 STREAK-РЕЖИМ: Непрерывные дни работы

### **✅ Реализовано:**
- Подсчет streak (непрерывных дней работы)
- Подстановка `[streak_days]` в тексты уровня 0
- Интеграция с ButtonMatrix для расчета планок

### **❌ В разработке: Специальные тексты для ключевых дней**
- **День 1:** "Вы начали! Начало положено!"
- **День 2:** "Уже 2 дня! Сохраняйте темп."
- **День 3:** "Третий день подряд!"
- **День 7:** "Отличная неделя!"
- **День 14:** "Две недели стабильности!"
- **День 30:** "Месяц продуктивности - вы в топе!"
- **День 50:** "Полтора месяца постоянства!"
- **День 100:** "Сотня дней - это мастерство!"

### **Промежуточные дни (универсальные):**
- Дни 4-6: "Так держать, уже [streak_days] дней!"
- Дни 8-13: "Уже [streak_days] дней подряд!"
- Дни 15-29: "[streak_days] дней стабильной работы!"

### **Правила streak:**
- **Только непрерывные дни** работы над приоритетным проектом
- **Сброс при пропуске:** Если день без работы → streak = 0
- **Минимум для streak:** Хотя бы один завершенный интервал в день

## ⚠️ РЕЖИМ ВОЗВРАЩЕНИЯ: Дни без работы

### **🟡 Уровень 1: 1 день без работы - ВАЖНЫЙ**
> **Философия:** Развилка - либо возвращение, либо деградация на недели

**Маппинг:** `daysWithoutWork == 1` → `verticalLevel = 1`

**Примеры заголовков:**
- "Один день - не страшно!"
- "Возвращаемся в ритм"
- "Новый день - новый старт"

**Эскалация по горизонтали:**
1. **1-е сообщение:** Мотивация + "лучшее время сейчас"
2. **2-е сообщение:** "Шанс еще есть" + "сниженная планка"
3. **3-е сообщение:** "Совсем короткий интервал" + "сохранит momentum"
4. **4-е сообщение:** "Хотя бы минимум" + "просто открыть проект"

### **🟠 Уровень 2: 2-3 дня без работы**
**Маппинг:** `daysWithoutWork == 2-3` → `verticalLevel = 2`
**Философия:** "Пора возвращаться! Несколько дней перерыва - не критично, но затягивать нельзя"

### **🔴 Уровень 3: 4-6 дней без работы**
**Маппинг:** `daysWithoutWork == 4-6` → `verticalLevel = 3`
**Философия:** "Серьезно, пора работать! Почти неделя без прогресса - время что-то менять"

### **🚨 Уровень 4: 7+ дней без работы**
**Маппинг:** `daysWithoutWork >= 7` → `verticalLevel = 4`
**Философия:** "КРИТИЧЕСКАЯ СИТУАЦИЯ! Неделя без работы. Этот проект все еще важен?"


## 🎭 Система слоев для создания сообщений

### **Принцип:** Каждое сообщение = сумма слоев

Сообщение создается путем **наложения нескольких слоев** друг на друга:

**СЛОЙ 1: Горизонтальная эскалация (универсальная временная срочность)**
**СЛОЙ 2: Вертикальная философия (streak ИЛИ дни без работы)**
**СЛОЙ 3: Дополнительные модификаторы (будущее развитие)**

### **🔄 СЛОЙ 1: Горизонтальная эскалация (универсальный)**

> **Ключевой принцип:** НЕ упоминаем конкретное время сессии в тексте!

**Почему:** У разных пользователей разная стартовая планка (15 мин vs 50 мин). При эскалации планка уменьшается пропорционально, но мы говорим универсально.

**Логика планок:**
- **1-е сообщение:** 100% от стартовой планки
- **2-е сообщение:** 50% от стартовой планки
- **3-е сообщение:** 25% от стартовой планки
- **4-е сообщение:** Фиксированно 3 минуты (план минимум)

**Универсальные принципы (работают для streak И возвращения):**
1. **1-е сообщение:** "Лучшее время начать - прямо сейчас"
2. **2-е сообщение:** "Шанс еще есть, но время идет"
3. **3-е сообщение:** "Время уходит, но еще можно"
4. **4-е сообщение:** "Последний шанс начать сегодня" (план минимум)

### **📊 СЛОЙ 2: Вертикальная философия (дни без работы)**

**Каждый уровень имеет свой тон и подход:**

- **Уровень 1 (1 день):** Развилка - либо возвращение, либо деградация на недели
- **Уровень 2 (2-3 дня):** Пора возвращаться! Перерыв не критичен, но затягивать нельзя
- **Уровень 3 (4-6 дней):** Серьезно, пора работать! Почти неделя без прогресса
- **Уровень 4 (7+ дней):** КРИТИЧЕСКАЯ СИТУАЦИЯ! Этот проект все еще важен?

### **🎯 СЛОЙ 3: Дополнительные модификаторы (будущее)**

**Потенциальные слои для развития:**
- **Время года/сезон:** Зимняя мотивация vs летняя энергия
- **День недели:** Понедельник vs пятница
- **Streak-бонусы:** Особые сообщения для круглых дат (7, 14, 30 дней)
- **Персонализация:** Учет предпочтений пользователя

> **Пока не реализовано** - временная срочность теперь входит в СЛОЙ 1

### **🔧 Как слои работают вместе:**

**Пример для "1 день без работы, 4-е сообщение":**
- **СЛОЙ 1:** Минимальный интервал (3 мин)
- **СЛОЙ 2:** Тон развилки (либо возвращение, либо деградация)
- **СЛОЙ 3:** Последний шанс сегодня

**Результат:** "Последний шанс не дать одному дню превратиться в недели. Минимальный интервал - просто открыть проект."

**Особенность уровня 4 (7+ дней):**
> ✅ **ПОДТВЕРЖДЕНО:** Все сообщения по горизонтали = 3 минуты (план минимум)
>
> **Логика:** Вертикальная планка = 3 мин, горизонтальная адаптация:
> - 1-е: max(3 мин, 3 мин) = 3 мин
> - 2-е: max(3×0.5, 3 мин) = 3 мин
> - 3-е: max(3×0.25, 3 мин) = 3 мин
> - 4-е: 3 мин (фиксированно)
>
> **Принцип текстов:** Акцент НЕ на времени, а на максимальном вовлечении и критичности ситуации

## 🎨 Практическое применение системы слоев

### **Алгоритм создания сообщения:**

1. **Определить слои:**
   - СЛОЙ 1: Какая горизонтальная позиция? (1-е, 2-е, 3-е, 4-е сообщение) → временная срочность
   - СЛОЙ 2: Streak или дни без работы? → философия и тон сообщения

2. **Наложить слои:**
   - Взять базовый тон из СЛОЯ 2 (streak поддержка ИЛИ философия возвращения)
   - Добавить временную срочность из СЛОЯ 1 (лучшее время сейчас → последний шанс)

3. **НЕ упоминать конкретное время** в тексте

### **Примеры наложения слоев:**

**Пример 1: "1 день без работы, 1-е сообщение"**
- СЛОЙ 1: "Лучшее время начать - прямо сейчас"
- СЛОЙ 2: "Развилка - либо возвращение, либо деградация"
- **Результат:** "Один день - не страшно! Сейчас лучшее время вернуться к [focused_project]"

**Пример 2: "7+ дней без работы, 4-е сообщение"**
- СЛОЙ 1: "Последний шанс начать сегодня"
- СЛОЙ 2: "КРИТИЧЕСКАЯ СИТУАЦИЯ! Проект важен?"
- **Результат:** "Неделя без работы... Этот проект все еще важен? Последний шанс сегодня - просто открыть файл"

**Пример 3: "Streak 5 дней, 2-е сообщение"**
- СЛОЙ 1: "Шанс еще есть, но время идет"
- СЛОЙ 2: "Уже 5 дней работы! Продолжаем streak"
- **Результат:** "Уже 5 дней работы над [focused_project]! Шанс еще есть продолжить streak"

**Пример 4: "2-3 дня без работы, 3-е сообщение"**
- СЛОЙ 1: "Время уходит, но еще можно"
- СЛОЙ 2: "Пора возвращаться! Затягивать нельзя"
- **Результат:** "Пора возвращаться к [focused_project]! Время уходит, но еще можно наверстать"



## 🔧 Техническая реализация

### **Матрица 5×4 (20 базовых сообщений):**
- **ВЕРТИКАЛЬ (0-4):** Уровни вовлечения (0=streak, 1-4=дни без работы)
- **ГОРИЗОНТАЛЬ (0-3):** Номер попытки в течение дня (эскалация планки)

### **Алгоритм выбора сообщения:**
1. **Определить режим:** Streak (работал сегодня) или Возвращение (дни без работы)
2. **Выбрать базовый текст** из матрицы по координатам
3. **Подставить переменные:** [streak_days], [focused_project], [current_bar]
4. **Будущее:** Рандомизация из нескольких вариантов

## 📏 Ограничения UI

- **Заголовок:** максимум 50 символов
- **Подзаголовок:** максимум 120 символов
- **Кнопка:** максимум 25 символов

> ⚠️ **Важно:** Превышение лимитов приведет к обрезанию текста в интерфейсе

## 🎲 Система рандомизации (планируется)

### **Множественные варианты для каждой ячейки:**
```json
{
  "1": { // 1 день без работы
    "0": { // 1-е сообщение
      "titles": [
        "Один день - не страшно!",
        "Возвращаемся в ритм",
        "Новый день - новый старт"
      ],
      "subtitles": [
        "Сейчас лучшее время вернуться...",
        "Важно начать сегодня...",
        "Утренняя энергия + сниженная планка..."
      ]
    }
  }
}
```

### **Алгоритм выбора:**
1. Определить координаты в матрице (дни без работы × попытка)
2. Проверить streak и добавить модификатор если нужно
3. Случайно выбрать заголовок и подзаголовок из доступных вариантов
4. Подставить переменные [focused_project], [current_bar], [streak_days]

## 📋 Переменные для подстановки

### **Доступные переменные:**
- `[focused_project]` - название приоритетного проекта
- `[current_bar]` - текущая планка (время сессии)
- `[streak_days]` - количество дней streak
- `[time_of_day]` - время дня (утро/день/вечер)

### **Примеры использования:**
- "Уже [streak_days] дней работы над [focused_project]!"
- "Попробуем [current_bar] минут на [focused_project]"
- "Хорошее [time_of_day] для возвращения к [focused_project]"

## ⚠️ Текущие ограничения и следующие шаги

### **✅ Что работает:**
- Полностью реализована streak-инфраструктура
- Корректно подсчитывает и хранит streak (непрерывные дни работы)
- Подставляет `[streak_days]` в тексты уровня 0
- Интегрировано с отладочным окном (радиокнопки, stepper)
- ButtonMatrix корректно рассчитывает планки для streak (vertical = -1)
- Система персистентности (WorkDay, workHistory)

### **❌ Что еще НЕ реализовано:**
- **Специальные тексты для ключевых дней streak** (2, 3, 7, 14, 30, 50, 100)
- **Логика выбора между специальными и обычными сообщениями**
- **Система рандомизации текстов**
- **Множественные варианты заголовков/подзаголовков**

### **🎯 Следующие приоритеты:**

**ПРИОРИТЕТ 1: Специальные сообщения для ключевых дней streak**
- [ ] Создать `StreakMessageMatrix` для специальных дней (2, 3, 7, 14, 30, 50, 100)
- [ ] Реализовать логику выбора: специальное сообщение VS обычное с подстановкой
- [ ] Написать фактические тексты для всех ключевых дней
- [ ] Интегрировать с `createEngagementMessage()` в EarlyEngagementSystem

**ПРИОРИТЕТ 2: Система множественных вариантов текстов**
- [ ] Создать JSON-структуру для хранения вариантов заголовков/подзаголовков
- [ ] Реализовать `TextVariationManager` для случайного выбора
- [ ] Заменить hardcoded сообщения в MessageConstructionMatrix

**ПРИОРИТЕТ 3: Финальная архитектура**
- [ ] Создать `MessagePersonalizationManager` для подстановки переменных
- [ ] Интегрировать все компоненты в единую систему
- [ ] Добавить поддержку времени дня в streak-сообщения
