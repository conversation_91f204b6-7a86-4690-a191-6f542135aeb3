# 🎭 Элегантное решение: Матрица заголовков и подзаголовков

> **Статус:** ✅ Архитектурное решение принято. Готово к реализации.

## 🎯 Суть решения

**Ключевая идея:** Разделить ответственность между заголовком и подзаголовком для минимизации объема работы при максимальной гибкости.

### **📋 Принцип разделения:**
- **ЗАГОЛОВОК** = Уровень (философия) + Горизонталь (срочность) = уникальный для каждой ячейки
- **ПОДЗАГОЛОВОК** = Универсальная горизонтальная эскалация = 4 варианта на ВСЕ уровни

## 📊 Объем работы

### **⚠️ Режим ВОЗВРАЩЕНИЯ:**
- **Заголовки:** 4 уровня × 4 горизонтали = **16 уникальных заголовков**
- **Подзаголовки:** **4 универсальных** (переиспользуются для всех уровней)
- **Итого:** 20 текстов

### **🔥 Режим STREAK:**
- **Заголовки:** **4 базовых с переменными** + специальные для ключевых дней
- **Подзаголовки:** **Те же 4 универсальных**
- **Итого:** 8 текстов (+ специальные)

### **🎯 ОБЩИЙ ИТОГ:**
**24 уникальных текста** вместо 40+ в полной матрице = **экономия 40%**

## 🔄 Универсальные подзаголовки (горизонтальная эскалация)

> **Работают для ВСЕХ уровней и режимов**

1. **1-е сообщение:** "Лучшее время начать - прямо сейчас"
2. **2-е сообщение:** "Шанс еще есть, но время идет"  
3. **3-е сообщение:** "Время уходит, но еще можно"
4. **4-е сообщение:** "Последний шанс начать сегодня"

## ⚠️ Заголовки для режима ВОЗВРАЩЕНИЯ

### **🟢 Уровень 1: 1 день без работы**
**Философия:** "Развилка - либо возвращение, либо деградация"

1. **1-е сообщение:** "Вчера не работал - не страшно!"
2. **2-е сообщение:** "Давай начинать! Вчера пропустил, но шанс есть"
3. **3-е сообщение:** "Не хочешь выпадать из ритма?"
4. **4-е сообщение:** "Один день может стать неделей..."

### **🟡 Уровень 2: 2-3 дня без работы**
**Философия:** "Пора возвращаться! Перерыв не критичен, но затягивать нельзя"

1. **1-е сообщение:** "Пора возвращаться к работе"
2. **2-е сообщение:** "Перерыв затягивается..."
3. **3-е сообщение:** "Уже [X] дня без прогресса"
4. **4-е сообщение:** "Не дай перерыву стать привычкой"

### **🟠 Уровень 3: 4-6 дней без работы**
**Философия:** "Серьезно, пора работать! Почти неделя без прогресса"

1. **1-е сообщение:** "Серьезно, пора работать!"
2. **2-е сообщение:** "Почти неделя без прогресса..."
3. **3-е сообщение:** "Ситуация выходит из-под контроля"
4. **4-е сообщение:** "Неделя на носу - последний шанс"

### **🔴 Уровень 4: 7+ дней без работы**
**Философия:** "КРИТИЧЕСКАЯ СИТУАЦИЯ! Этот проект все еще важен?"

1. **1-е сообщение:** "Неделя без работы - но еще не поздно"
2. **2-е сообщение:** "Этот проект все еще важен?"
3. **3-е сообщение:** "Критическая ситуация с проектом"
4. **4-е сообщение:** "Неделя без работы... Последний шанс?"

## 🔥 Заголовки для режима STREAK

### **📈 Базовые заголовки с переменными:**
1. **1-е сообщение:** "Уже [streak_days] [день/дня/дней] работы!"
2. **2-е сообщение:** "Не хочется терять [streak_days] [день/дня/дней] прогресса"
3. **3-е сообщение:** "[streak_days] [день/дня/дней] подряд - отличный темп!"
4. **4-е сообщение:** "Streak [streak_days] [день/дня/дней] - продолжаем!"

### **📝 Правила склонения:**
- **1, 21, 31, 41...** → "день" (1 день, 21 день)
- **2-4, 22-24, 32-34...** → "дня" (2 дня, 3 дня, 23 дня)
- **5-20, 25-30, 35-40...** → "дней" (5 дней, 10 дней, 25 дней)

### **🎉 Специальные заголовки для ключевых дней:**
> **Переопределяют базовые для особых дат**

**День 7:**
- "Целая неделя работы!" (вместо "Уже 7 дней работы!")

**День 14:**
- "Две недели стабильности!" (вместо "Уже 14 дней работы!")

**День 30:**
- "Месяц продуктивности!" (вместо "Уже 30 дней работы!")

**День 50:**
- "Полтора месяца постоянства!" (вместо "Уже 50 дней работы!")

**День 100:**
- "Сотня дней мастерства!" (вместо "Уже 100 дней работы!")

## 🎨 Примеры работы системы

### **Пример 1: Возвращение, уровень 1, 1-е сообщение**
- **Заголовок:** "Вчера не работал - не страшно!"
- **Подзаголовок:** "Лучшее время начать - прямо сейчас"

### **Пример 2: Возвращение, уровень 4, 4-е сообщение**
- **Заголовок:** "Неделя без работы... Последний шанс?"
- **Подзаголовок:** "Последний шанс начать сегодня"

### **Пример 3: Streak 5 дней, 2-е сообщение**
- **Заголовок:** "Не хочется терять 5 дней прогресса" (5 → "дней")
- **Подзаголовок:** "Шанс еще есть, но время идет"

### **Пример 4: Streak 30 дней, 1-е сообщение**
- **Заголовок:** "Месяц продуктивности!" (специальный)
- **Подзаголовок:** "Лучшее время начать - прямо сейчас"

### **Дополнительные примеры склонения:**
- **Streak 1 день:** "Уже 1 день работы!" (1 → "день")
- **Streak 2 дня:** "Уже 2 дня работы!" (2 → "дня")
- **Streak 21 день:** "Уже 21 день работы!" (21 → "день")
- **Streak 23 дня:** "Уже 23 дня работы!" (23 → "дня")

## ✅ Преимущества решения

1. **Минимальный объем работы:** 24 текста вместо 40+
2. **Максимальная гибкость:** Каждый заголовок точно отражает ситуацию
3. **Переиспользование:** 4 подзаголовка работают везде
4. **Логичность:** Четкое разделение ответственности
5. **Масштабируемость:** Легко добавлять новые уровни
6. **Элегантность:** Простая и понятная система

## 🔧 Техническая реализация склонения

### **Swift функция для склонения:**
```swift
func getDaysDeclension(_ days: Int) -> String {
    let lastDigit = days % 10
    let lastTwoDigits = days % 100

    // Исключения для 11-14
    if lastTwoDigits >= 11 && lastTwoDigits <= 14 {
        return "дней"
    }

    switch lastDigit {
    case 1:
        return "день"
    case 2, 3, 4:
        return "дня"
    default:
        return "дней"
    }
}
```

### **Примеры работы:**
- `getDaysDeclension(1)` → "день" → "Уже 1 день работы!"
- `getDaysDeclension(2)` → "дня" → "Уже 2 дня работы!"
- `getDaysDeclension(5)` → "дней" → "Уже 5 дней работы!"
- `getDaysDeclension(21)` → "день" → "Уже 21 день работы!"
- `getDaysDeclension(23)` → "дня" → "Уже 23 дня работы!"

## 🚀 Следующие шаги

1. **Финализировать тексты** заголовков для возвращения (16 штук)
2. **Создать логику выбора** между базовыми и специальными streak заголовками
3. **Реализовать функцию склонения** для переменной [день/дня/дней]
4. **Интегрировать в MessageConstructionMatrix**
5. **Протестировать** все комбинации
6. **Добавить поддержку переменных** [streak_days], [focused_project]

---

> **Результат:** Элегантная система, которая решает проблему объема работы при сохранении максимальной гибкости и точности сообщений.
